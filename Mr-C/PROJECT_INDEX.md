# Mr.C Project Index

## Project Overview
**Name:** Mr.C  
**Type:** React Native + Expo Application  
**Framework:** Expo Router with TypeScript  
**Version:** 1.0.0  

## Project Structure

### 📁 Root Directory
```
Mr-C/
├── 📱 app/                    # Main application code (Expo Router)
├── 🎨 assets/                 # Static assets (images, fonts)
├── 🧩 components/             # Reusable React components
├── 🔧 constants/              # App constants and configuration
├── 🪝 hooks/                  # Custom React hooks
├── 📦 node_modules/           # Dependencies
├── 📜 scripts/                # Build and utility scripts
├── 📋 package.json            # Project configuration
├── 🔧 tsconfig.json           # TypeScript configuration
├── 🔍 eslint.config.js        # ESLint configuration
└── 📖 README.md               # Project documentation
```

### 📱 App Directory (Expo Router)
```
app/
├── (tabs)/                    # Tab-based navigation group
│   ├── _layout.tsx           # Tab layout configuration
│   ├── index.tsx             # Home tab screen
│   └── explore.tsx           # Explore tab screen
├── _layout.tsx               # Root layout configuration
└── +not-found.tsx            # 404 error page
```

### 🧩 Components Directory
```
components/
├── ui/                       # UI component library
├── Collapsible.tsx          # Collapsible content component
├── ExternalLink.tsx         # External link component
├── HapticTab.tsx            # Tab with haptic feedback
├── HelloWave.tsx            # Animated wave component
├── ParallaxScrollView.tsx   # Parallax scroll view
├── ThemedText.tsx           # Themed text component
└── ThemedView.tsx           # Themed view component
```

### 🎨 Assets Directory
```
assets/
├── fonts/                   # Custom fonts
└── images/                  # Images and icons
```

### 🔧 Constants Directory
```
constants/
└── Colors.ts               # Color theme definitions
```

### 🪝 Hooks Directory
```
hooks/
├── useColorScheme.ts       # Color scheme hook
├── useColorScheme.web.ts   # Web-specific color scheme
└── useThemeColor.ts        # Theme color hook
```

## 📦 Dependencies

### Core Dependencies
- **React:** 19.0.0
- **React Native:** 0.79.4
- **Expo:** ~53.0.13
- **Expo Router:** ~5.1.1

### Navigation
- **@react-navigation/native:** ^7.1.6
- **@react-navigation/bottom-tabs:** ^7.3.10
- **@react-navigation/elements:** ^2.3.8

### UI & Animation
- **react-native-reanimated:** ~3.17.4
- **react-native-gesture-handler:** ~2.24.0
- **expo-blur:** ~14.1.5
- **expo-haptics:** ~14.1.4

### Development Tools
- **TypeScript:** ~5.8.3
- **ESLint:** ^9.25.0
- **@babel/core:** ^7.25.2

## 🚀 Available Scripts

```bash
npm start          # Start Expo development server
npm run android    # Start on Android device/emulator
npm run ios        # Start on iOS device/simulator
npm run web        # Start web version
npm run lint       # Run ESLint
npm run reset-project  # Reset project to initial state
```

## 🏗️ Architecture

### Navigation Structure
- **Expo Router** with file-based routing
- **Tab Navigation** as primary navigation pattern
- **TypeScript** for type safety

### Theming System
- **Dynamic theming** with light/dark mode support
- **Themed components** (ThemedText, ThemedView)
- **Color constants** centralized in constants/Colors.ts

### Component Architecture
- **Reusable components** in components/ directory
- **Custom hooks** for shared logic
- **Platform-specific** implementations where needed

## 🔧 Configuration Files

### TypeScript Configuration
- **tsconfig.json** - TypeScript compiler options
- **expo-env.d.ts** - Expo environment types

### Linting & Code Quality
- **eslint.config.js** - ESLint rules and configuration
- **expo lint** integration

### Build Configuration
- **app.json** - Expo app configuration
- **babel.config.js** - Babel transpilation settings

## 📱 Platform Support
- ✅ **iOS** - Native iOS app
- ✅ **Android** - Native Android app  
- ✅ **Web** - Progressive Web App

## 🎯 Key Features
- **File-based routing** with Expo Router
- **Tab navigation** with haptic feedback
- **Dark/Light theme** support
- **TypeScript** throughout
- **Cross-platform** compatibility
- **Modern React** patterns and hooks

## 📝 Development Notes
- Uses **Expo SDK 53**
- **React 19** with latest features
- **TypeScript 5.8** for enhanced type checking
- **ESLint** for code quality
- **Metro bundler** for fast refresh

---
*Generated on: $(date)*
*Project: Mr.C React Native Application*
